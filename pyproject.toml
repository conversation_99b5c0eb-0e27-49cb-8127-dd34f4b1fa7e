[project]
name = "termoj"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "httpx>=0.28.1",
    "platformdirs>=4.3.8",
    "pydantic>=2.11.5",
    "textual>=3.3.0",
]

[project.optional-dependencies]
dev = ["pytest>=8.4.0", "pytest-asyncio>=1.0.0", "textual-dev>=1.7.0"]

[project.scripts]
termoj = "termoj.main:run_app"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = ["-v", "--strict-markers", "--strict-config", "--tb=short"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
