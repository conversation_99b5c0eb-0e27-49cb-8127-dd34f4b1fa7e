"""
Data models for the ACMOJ API.

This module contains all Pydantic models that correspond to the API schemas
defined in the OpenAPI specification.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum

from pydantic import BaseModel


# Enums
class SubmissionStatus(str, Enum):
    """Enumeration of possible submission statuses."""
    ACCEPTED = "accepted"
    WRONG_ANSWER = "wrong_answer"
    COMPILE_ERROR = "compile_error"
    RUNTIME_ERROR = "runtime_error"
    TIME_LIMIT_EXCEEDED = "time_limit_exceeded"
    MEMORY_LIMIT_EXCEEDED = "memory_limit_exceeded"
    DISK_LIMIT_EXCEEDED = "disk_limit_exceeded"
    MEMORY_LEAK = "memory_leak"
    PENDING = "pending"
    COMPILING = "compiling"
    JUDGING = "judging"
    VOID = "void"
    ABORTED = "aborted"
    SKIPPED = "skipped"
    SYSTEM_ERROR = "system_error"
    BAD_PROBLEM = "bad_problem"
    UNKNOWN_ERROR = "unknown_error"


class SubmissionLanguage(str, Enum):
    """Enumeration of supported programming languages."""
    CPP = "cpp"
    PYTHON = "python"
    GIT = "git"
    VERILOG = "verilog"
    QUIZ = "quiz"


class ProblemsetType(str, Enum):
    """Enumeration of problemset types."""
    CONTEST = "contest"
    HOMEWORK = "homework"
    EXAM = "exam"


# Core Data Models
class Profile(BaseModel):
    """User profile information."""
    username: str
    friendly_name: str
    student_id: Optional[str] = None


class CourseTag(BaseModel):
    """Course tag information."""
    id: int
    name: str


class Term(BaseModel):
    """Academic term information."""
    id: int
    name: str
    start_time: datetime
    end_time: datetime


class Course(BaseModel):
    """Course (class) information."""
    id: int
    name: str
    description: str
    tag: Optional[CourseTag] = None
    term: Optional[Term] = None
    url: str
    join_url: Optional[str] = None
    quit_url: Optional[str] = None
    html_url: str


class ProblemBrief(BaseModel):
    """Brief problem information for listings."""
    id: int
    title: Optional[str] = None
    url: Optional[str] = None
    submit_url: Optional[str] = None
    html_url: Optional[str] = None


class ResourceUsage(BaseModel):
    """Resource usage limits and measurements."""
    time_msecs: int
    memory_bytes: int
    file_count: int
    file_size_bytes: int


class TestpointSummary(BaseModel):
    """Test point summary information."""
    id: str
    limits: Optional[ResourceUsage] = None


class SubtaskSummary(BaseModel):
    """Subtask summary information."""
    id: str
    name: str
    testpoints: List[TestpointSummary]
    score: float


class JudgePlanSummary(BaseModel):
    """Judge plan summary information."""
    subtasks: List[SubtaskSummary]


class ProblemExample(BaseModel):
    """Problem example input/output."""
    name: Optional[str] = None
    input: Optional[str] = None
    output: Optional[str] = None
    description: Optional[str] = None


class ProblemAttachment(BaseModel):
    """Problem attachment information."""
    name: str
    size_bytes: int
    url: str


class Problem(BaseModel):
    """Detailed problem information."""
    id: int
    title: str
    description: Optional[str] = None
    input: Optional[str] = None
    output: Optional[str] = None
    examples: List[ProblemExample] = []
    example_input: Optional[str] = None
    example_output: Optional[str] = None
    data_range: Optional[str] = None
    languages_accepted: List[SubmissionLanguage] = []
    plan_summary: Optional[JudgePlanSummary] = None
    attachments: List[ProblemAttachment] = []
    allow_public_submissions: bool


class SubmissionBrief(BaseModel):
    """Brief submission information for listings."""
    id: int
    friendly_name: str
    problem: ProblemBrief
    status: SubmissionStatus
    language: SubmissionLanguage
    created_at: datetime
    url: Optional[str] = None
    html_url: Optional[str] = None


class Submission(BaseModel):
    """Detailed submission information."""
    id: int
    friendly_name: str
    problem: ProblemBrief
    public: bool
    language: SubmissionLanguage
    score: int
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    time_msecs: Optional[int] = None
    memory_bytes: Optional[int] = None
    status: SubmissionStatus
    should_auto_reload: bool
    should_show_score: bool
    created_at: datetime
    code_url: str
    abort_url: Optional[str] = None
    html_url: str


class Problemset(BaseModel):
    """Problemset (contest/homework) information."""
    id: int
    course: Course
    name: str
    description: str
    allowed_languages: List[SubmissionLanguage] = []
    start_time: datetime
    end_time: datetime
    late_submission_deadline: Optional[datetime] = None
    type: ProblemsetType
    problems: List[ProblemBrief] = []
    url: str
    join_url: Optional[str] = None
    quit_url: Optional[str] = None
    html_url: str


# API Response Models
class TokenResponse(BaseModel):
    """OAuth token response."""
    access_token: str
    token_type: str
    expires_in: int
    scope: str


class ProblemsResponse(BaseModel):
    """Response for problem listings."""
    problems: List[ProblemBrief]
    next: Optional[str] = None


class SubmissionsResponse(BaseModel):
    """Response for submission listings."""
    submissions: List[SubmissionBrief]
    next: Optional[str] = None


class CoursesResponse(BaseModel):
    """Response for course listings."""
    courses: List[Course]
    next: Optional[str] = None


class ProblemsetsResponse(BaseModel):
    """Response for problemset listings."""
    problemsets: List[Problemset]


class UserCoursesResponse(BaseModel):
    """Response for user's courses."""
    courses: List[Course]


class UserProblemsetsResponse(BaseModel):
    """Response for user's problemsets."""
    problemsets: List[Problemset]


class SubmitResponse(BaseModel):
    """Response for code submission."""
    id: int
