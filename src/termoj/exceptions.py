"""
Exception classes for the ACMOJ API client.

This module defines the exception hierarchy used throughout the termoj application
for handling API errors and other exceptional conditions.
"""

from typing import Optional
import httpx


class ACMOJAPIError(Exception):
    """
    Base exception for ACMOJ API errors.
    
    This is the base class for all API-related exceptions. It provides
    common functionality for storing HTTP status codes and response objects.
    """
    
    def __init__(
        self, 
        message: str, 
        status_code: Optional[int] = None, 
        response: Optional[httpx.Response] = None
    ):
        """
        Initialize the API error.
        
        Args:
            message: Human-readable error message
            status_code: HTTP status code if available
            response: The HTTP response object if available
        """
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.response = response
    
    def __str__(self) -> str:
        """Return a string representation of the error."""
        if self.status_code:
            return f"HTTP {self.status_code}: {self.message}"
        return self.message


class AuthenticationError(ACMOJAPIError):
    """
    Raised when authentication fails (401/403).
    
    This exception is raised when the API returns a 401 (Unauthorized) or
    403 (Forbidden) status code, indicating authentication or authorization
    issues.
    """
    pass


class NotFoundError(ACMOJAPIError):
    """
    Raised when a resource is not found (404).
    
    This exception is raised when the API returns a 404 (Not Found) status
    code, indicating that the requested resource does not exist.
    """
    pass


class ValidationError(ACMOJAPIError):
    """
    Raised when request validation fails (400/422).
    
    This exception is raised when the API returns a 400 (Bad Request) or
    422 (Unprocessable Entity) status code, indicating that the request
    data is invalid or malformed.
    """
    pass


class ServerError(ACMOJAPIError):
    """
    Raised when server encounters an error (5xx).
    
    This exception is raised when the API returns a 5xx status code,
    indicating a server-side error.
    """
    pass


class ConfigurationError(Exception):
    """
    Raised when there are configuration-related errors.
    
    This exception is raised when there are issues with the application
    configuration, such as missing or invalid configuration files.
    """
    pass


class FileNotFoundError(Exception):
    """
    Raised when a required file is not found.
    
    This exception is raised when the application cannot find a required
    file, such as source code files for submission.
    """
    pass
