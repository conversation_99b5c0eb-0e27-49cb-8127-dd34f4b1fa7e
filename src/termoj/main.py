"""
Main entry point for the termoj TUI application.

This module provides the main entry point for the termoj application,
which will eventually contain the Textual-based TUI interface.
"""

import asyncio
import sys
from typing import Op<PERSON>

from .api_client import ACMOJClient
from .exceptions import ACMOJ<PERSON>IError


def run_app():
    """
    Main entry point for the termoj application.
    
    This function will be called when the user runs the `termoj` command.
    Currently it's a placeholder that will be replaced with the actual TUI.
    """
    print("🚀 Termoj - Terminal Online Judge")
    print("=" * 40)
    print("This is a placeholder for the TUI application.")
    print("The API client has been successfully set up!")
    print()
    print("To test the API client, you can use:")
    print("  python -m pytest tests/")
    print()
    print("For development, see the example files:")
    print("  - examples/basic_usage.py")
    print("  - examples/test_client.py")


async def demo_api_client(base_url: str, token: str):
    """
    Demonstration of the API client functionality.
    
    This is a helper function that shows how to use the API client.
    It will be integrated into the TUI later.
    
    Args:
        base_url: The API base URL
        token: The authentication token
    """
    try:
        async with ACMOJClient(base_url, token) as client:
            print("🔗 Testing API connection...")
            
            # Test user profile
            profile = await client.get_user_profile()
            print(f"✅ Connected as: {profile.friendly_name} ({profile.username})")
            
            # Test problem listing
            problems_response = await client.list_problems()
            print(f"📚 Found {len(problems_response.problems)} problems")
            
            # Test user courses
            courses = await client.get_user_courses()
            print(f"🎓 Enrolled in {len(courses)} courses")
            
            return True
            
    except ACMOJAPIError as e:
        print(f"❌ API Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


if __name__ == "__main__":
    run_app()
