["tests/test_api_client.py::TestACMOJClientInit::test_base_url_trailing_slash_removal", "tests/test_api_client.py::TestACMOJClientInit::test_client_initialization", "tests/test_api_client.py::TestACMOJClientInit::test_context_manager", "tests/test_api_client.py::TestErrorHandling::test_handle_400_error", "tests/test_api_client.py::TestErrorHandling::test_handle_401_error", "tests/test_api_client.py::TestErrorHandling::test_handle_403_error", "tests/test_api_client.py::TestErrorHandling::test_handle_404_error", "tests/test_api_client.py::TestErrorHandling::test_handle_500_error", "tests/test_api_client.py::TestErrorHandling::test_handle_success_response", "tests/test_api_client.py::TestProblemMethods::test_get_problem", "tests/test_api_client.py::TestProblemMethods::test_list_problems", "tests/test_api_client.py::TestProblemMethods::test_list_problems_with_filters", "tests/test_api_client.py::TestSubmissionMethods::test_abort_submission", "tests/test_api_client.py::TestSubmissionMethods::test_get_submission", "tests/test_api_client.py::TestSubmissionMethods::test_list_submissions", "tests/test_api_client.py::TestSubmissionMethods::test_list_submissions_with_filters", "tests/test_api_client.py::TestSubmissionMethods::test_submit_solution", "tests/test_api_client.py::TestUserMethods::test_get_user_courses", "tests/test_api_client.py::TestUserMethods::test_get_user_profile", "tests/test_exceptions.py::TestACMOJAPIError::test_basic_error", "tests/test_exceptions.py::TestACMOJAPIError::test_error_with_response", "tests/test_exceptions.py::TestACMOJAPIError::test_error_with_status_code", "tests/test_exceptions.py::TestSpecificErrors::test_authentication_error", "tests/test_exceptions.py::TestSpecificErrors::test_configuration_error", "tests/test_exceptions.py::TestSpecificErrors::test_not_found_error", "tests/test_exceptions.py::TestSpecificErrors::test_server_error", "tests/test_exceptions.py::TestSpecificErrors::test_validation_error", "tests/test_models.py::TestCourse::test_course_creation", "tests/test_models.py::TestEnums::test_problemset_type_enum", "tests/test_models.py::TestEnums::test_submission_language_enum", "tests/test_models.py::TestEnums::test_submission_status_enum", "tests/test_models.py::TestProblem::test_problem_creation", "tests/test_models.py::TestProblem::test_problem_with_examples", "tests/test_models.py::TestProfile::test_profile_creation", "tests/test_models.py::TestProfile::test_profile_validation_error", "tests/test_models.py::TestProfile::test_profile_without_student_id", "tests/test_models.py::TestSubmission::test_submission_creation"]