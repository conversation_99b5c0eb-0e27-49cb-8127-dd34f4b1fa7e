#!/usr/bin/env python3
"""
Test script for the ACMOJ API Client

This script demonstrates how to use the ACMOJClient to interact with the ACMOJ API.
Replace the base_url and token with actual values to test against a real server.
"""

import asyncio
from api_client import ACMOJClient, ACMOJAPIError, AuthenticationError, NotFoundError


async def main():
    """
    Example usage of the ACMOJ API client.
    
    This demonstrates the main functionality including:
    - User profile retrieval
    - Problem listing and details
    - Submission management
    - Course operations
    """
    
    # Configuration - replace with actual values
    base_url = "https://acm.sjtu.edu.cn/OnlineJudge/api/v1"
    token = "your-secret-api-token"  # Replace with actual token
    
    # Create client instance
    async with ACMOJClient(base_url=base_url, token=token) as client:
        try:
            print("🚀 Testing ACMOJ API Client")
            print("=" * 50)
            
            # Test 1: Get user profile
            print("\n📋 Getting user profile...")
            try:
                profile = await client.get_user_profile()
                print(f"✅ Hello, {profile.friendly_name}! (Username: {profile.username})")
                if profile.student_id:
                    print(f"   Student ID: {profile.student_id}")
            except AuthenticationError:
                print("❌ Authentication failed - please check your token")
                return
            except Exception as e:
                print(f"❌ Failed to get profile: {e}")
                return
            
            # Test 2: List problems
            print("\n🧩 Listing problems...")
            try:
                problems_response = await client.list_problems()
                problems = problems_response.problems
                print(f"✅ Found {len(problems)} problems")
                
                if problems:
                    first_problem = problems[0]
                    print(f"   First problem: #{first_problem.id}")
                    if first_problem.title:
                        print(f"   Title: {first_problem.title}")
                    
                    # Test 3: Get problem details
                    if first_problem.id:
                        print(f"\n📖 Getting details for problem #{first_problem.id}...")
                        try:
                            problem = await client.get_problem(first_problem.id)
                            print(f"✅ Problem title: {problem.title}")
                            print(f"   Allowed languages: {[lang.value for lang in problem.languages_accepted]}")
                            print(f"   Examples: {len(problem.examples)}")
                            if problem.examples:
                                first_example = problem.examples[0]
                                if first_example.input:
                                    print(f"   Sample input: {first_example.input[:50]}...")
                        except NotFoundError:
                            print("❌ Problem not found or not accessible")
                        except Exception as e:
                            print(f"❌ Failed to get problem details: {e}")
            
            except Exception as e:
                print(f"❌ Failed to list problems: {e}")
            
            # Test 4: List user's courses
            print("\n🎓 Getting user's courses...")
            try:
                courses = await client.get_user_courses()
                print(f"✅ User is enrolled in {len(courses)} courses")
                for course in courses[:3]:  # Show first 3 courses
                    print(f"   - {course.name}")
                    if course.description:
                        print(f"     Description: {course.description[:100]}...")
            except Exception as e:
                print(f"❌ Failed to get courses: {e}")
            
            # Test 5: List submissions
            print("\n📝 Listing recent submissions...")
            try:
                submissions_response = await client.list_submissions()
                submissions = submissions_response.submissions
                print(f"✅ Found {len(submissions)} submissions")
                
                for submission in submissions[:3]:  # Show first 3 submissions
                    print(f"   - Submission #{submission.id}")
                    print(f"     Problem: #{submission.problem.id}")
                    if submission.problem.title:
                        print(f"     Title: {submission.problem.title}")
                    print(f"     Status: {submission.status.value}")
                    print(f"     Language: {submission.language.value}")
                    print(f"     Created: {submission.created_at}")
                    print()
            except Exception as e:
                print(f"❌ Failed to list submissions: {e}")
            
            # Test 6: Get meta information
            print("\n🔧 Getting meta information...")
            try:
                judge_status_info = await client.get_judge_status_info()
                print(f"✅ Available judge statuses: {len(judge_status_info)}")
                
                language_info = await client.get_language_info()
                print(f"✅ Available languages: {len(language_info)}")
                
                runner_status = await client.get_runner_status()
                print(f"✅ Active runners: {len(runner_status)}")
                
            except Exception as e:
                print(f"❌ Failed to get meta information: {e}")
            
            print("\n🎉 API client test completed!")
            
        except ACMOJAPIError as e:
            print(f"❌ ACMOJ API Error: {e.message}")
            if e.status_code:
                print(f"   Status Code: {e.status_code}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")


def example_submission():
    """
    Example of how to submit a solution.
    
    Note: This is commented out to prevent accidental submissions during testing.
    """
    async def submit_example():
        base_url = "https://acm.sjtu.edu.cn/OnlineJudge/api/v1"
        token = "your-secret-api-token"
        
        async with ACMOJClient(base_url=base_url, token=token) as client:
            # Example C++ solution for A+B problem
            cpp_code = """
#include <iostream>
using namespace std;

int main() {
    int a, b;
    cin >> a >> b;
    cout << a + b << endl;
    return 0;
}
"""
            
            try:
                # Submit to problem 1000 (typically A+B problem)
                submission_id = await client.submit_solution(
                    problem_id=1000,
                    language="cpp",
                    code=cpp_code,
                    public=False
                )
                print(f"✅ Submitted solution! Submission ID: {submission_id}")
                
                # Check submission status
                submission = await client.get_submission(submission_id)
                print(f"📊 Status: {submission.status.value}")
                print(f"🔄 Should auto-reload: {submission.should_auto_reload}")
                
            except Exception as e:
                print(f"❌ Submission failed: {e}")
    
    # Uncomment the line below to run the submission example
    # asyncio.run(submit_example())


if __name__ == "__main__":
    print("ACMOJ API Client Test")
    print("Note: Replace the token and base_url with actual values to test against a real server.")
    print("This test will fail with authentication errors if using placeholder values.\n")
    
    asyncio.run(main())
