#!/usr/bin/env python3
"""
Example usage of the ACMOJ API Client

This example demonstrates the exact usage pattern requested in the original specification.
"""

import asyncio

# Assuming the generated code is in a file named api_client.py
from api_client import ACMOJClient, ACMOJAPIError


async def main():
    """
    This is an example of how the generated client should work.
    Replace with a real token and potentially a different base_url.
    """
    base_url = "https://acm.sjtu.edu.cn/OnlineJudge/api/v1"
    token = "your-secret-api-token"

    client = ACMOJClient(base_url=base_url, token=token)

    try:
        # Get user profile
        profile = await client.get_user_profile()
        print(f"Hello, {profile.friendly_name}!")

        # Get a specific problem
        problem = await client.get_problem(problem_id=1000)
        print(f"Fetched problem: {problem.title}")
        for example in problem.examples:
            print(f"  - Example Input: {example.input}")

        # List submissions for that problem
        submissions_response = await client.list_submissions(problem_id=1000)
        submissions = submissions_response.submissions
        print(f"Found {len(submissions)} submissions for this problem.")
        if submissions:
            first_submission = submissions[0]
            print(f"  - First submission status: {first_submission.status}")

    except ACMOJAPIError as e:
        print(f"An API error occurred: {e}")
    finally:
        # It's important to close the client session
        await client.close()


if __name__ == "__main__":
    asyncio.run(main())
