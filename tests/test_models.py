"""
Tests for the data models.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from termoj.models import (
    Profile, Course, Problem, Submission, SubmissionStatus, 
    SubmissionLanguage, ProblemsetType, ProblemExample
)


class TestProfile:
    """Test the Profile model."""
    
    def test_profile_creation(self):
        """Test creating a valid profile."""
        profile = Profile(
            username="testuser",
            friendly_name="Test User",
            student_id="123456789"
        )
        assert profile.username == "testuser"
        assert profile.friendly_name == "Test User"
        assert profile.student_id == "123456789"
    
    def test_profile_without_student_id(self):
        """Test creating a profile without student ID."""
        profile = Profile(
            username="testuser",
            friendly_name="Test User"
        )
        assert profile.username == "testuser"
        assert profile.friendly_name == "Test User"
        assert profile.student_id is None
    
    def test_profile_validation_error(self):
        """Test profile validation with missing required fields."""
        with pytest.raises(ValidationError):
            Profile(username="testuser")  # Missing friendly_name


class TestEnums:
    """Test the enum classes."""
    
    def test_submission_status_enum(self):
        """Test SubmissionStatus enum values."""
        assert SubmissionStatus.ACCEPTED == "accepted"
        assert SubmissionStatus.WRONG_ANSWER == "wrong_answer"
        assert SubmissionStatus.COMPILE_ERROR == "compile_error"
    
    def test_submission_language_enum(self):
        """Test SubmissionLanguage enum values."""
        assert SubmissionLanguage.CPP == "cpp"
        assert SubmissionLanguage.PYTHON == "python"
        assert SubmissionLanguage.GIT == "git"
    
    def test_problemset_type_enum(self):
        """Test ProblemsetType enum values."""
        assert ProblemsetType.CONTEST == "contest"
        assert ProblemsetType.HOMEWORK == "homework"
        assert ProblemsetType.EXAM == "exam"


class TestCourse:
    """Test the Course model."""
    
    def test_course_creation(self):
        """Test creating a valid course."""
        course = Course(
            id=1,
            name="Test Course",
            description="A test course",
            url="/api/v1/course/1",
            html_url="/course/1"
        )
        assert course.id == 1
        assert course.name == "Test Course"
        assert course.description == "A test course"
        assert course.tag is None
        assert course.term is None


class TestProblem:
    """Test the Problem model."""
    
    def test_problem_creation(self):
        """Test creating a valid problem."""
        problem = Problem(
            id=1000,
            title="A+B Problem",
            allow_public_submissions=True
        )
        assert problem.id == 1000
        assert problem.title == "A+B Problem"
        assert problem.allow_public_submissions is True
        assert problem.examples == []
        assert problem.languages_accepted == []
    
    def test_problem_with_examples(self):
        """Test creating a problem with examples."""
        example = ProblemExample(
            name="Sample 1",
            input="1 2",
            output="3",
            description="Basic addition"
        )
        problem = Problem(
            id=1000,
            title="A+B Problem",
            examples=[example],
            allow_public_submissions=True
        )
        assert len(problem.examples) == 1
        assert problem.examples[0].name == "Sample 1"
        assert problem.examples[0].input == "1 2"
        assert problem.examples[0].output == "3"


class TestSubmission:
    """Test the Submission model."""
    
    def test_submission_creation(self):
        """Test creating a valid submission."""
        from termoj.models import ProblemBrief
        
        problem_brief = ProblemBrief(id=1000, title="A+B Problem")
        submission = Submission(
            id=42,
            friendly_name="Test User",
            problem=problem_brief,
            public=False,
            language=SubmissionLanguage.CPP,
            score=100,
            status=SubmissionStatus.ACCEPTED,
            should_auto_reload=False,
            should_show_score=True,
            created_at=datetime.now(),
            code_url="/api/v1/submission/42/code",
            html_url="/code/42"
        )
        assert submission.id == 42
        assert submission.friendly_name == "Test User"
        assert submission.language == SubmissionLanguage.CPP
        assert submission.status == SubmissionStatus.ACCEPTED
        assert submission.score == 100
