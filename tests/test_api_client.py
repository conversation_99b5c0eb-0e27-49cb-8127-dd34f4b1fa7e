"""
Tests for the API client.
"""

from unittest.mock import AsyncMock, <PERSON><PERSON>

import httpx
import pytest

from termoj.api_client import ACMOJClient
from termoj.exceptions import (
    AuthenticationError,
    NotFoundError,
    ServerError,
    ValidationError,
)
from termoj.models import Profile, SubmissionLanguage, SubmissionStatus


class TestACMOJClientInit:
    """Test ACMOJClient initialization."""

    def test_client_initialization(self):
        """Test basic client initialization."""
        client = ACMOJClient("https://test.example.com/api/v1", "test-token")
        assert client.base_url == "https://test.example.com/api/v1"
        assert client.token == "test-token"
        assert isinstance(client.client, httpx.AsyncClient)

    def test_base_url_trailing_slash_removal(self):
        """Test that trailing slashes are removed from base URL."""
        client = ACMOJClient("https://test.example.com/api/v1/", "test-token")
        assert client.base_url == "https://test.example.com/api/v1"

    @pytest.mark.asyncio
    async def test_context_manager(self):
        """Test async context manager functionality."""
        async with ACMOJClient(
            "https://test.example.com/api/v1", "test-token"
        ) as client:
            assert isinstance(client, ACMOJClient)
        # Client should be closed after exiting context


class TestErrorHandling:
    """Test error handling in the API client."""

    def test_handle_401_error(self, api_client, mock_response):
        """Test handling of 401 authentication errors."""
        response = mock_response(status_code=401)

        with pytest.raises(AuthenticationError) as exc_info:
            api_client._handle_response_errors(response)

        assert "Authentication failed" in str(exc_info.value)
        assert exc_info.value.status_code == 401

    def test_handle_403_error(self, api_client, mock_response):
        """Test handling of 403 forbidden errors."""
        response = mock_response(status_code=403)

        with pytest.raises(AuthenticationError) as exc_info:
            api_client._handle_response_errors(response)

        assert "Access forbidden" in str(exc_info.value)
        assert exc_info.value.status_code == 403

    def test_handle_404_error(self, api_client, mock_response):
        """Test handling of 404 not found errors."""
        response = mock_response(status_code=404)

        with pytest.raises(NotFoundError) as exc_info:
            api_client._handle_response_errors(response)

        assert "Resource not found" in str(exc_info.value)
        assert exc_info.value.status_code == 404

    def test_handle_400_error(self, api_client, mock_response):
        """Test handling of 400 validation errors."""
        response = mock_response(status_code=400)

        with pytest.raises(ValidationError) as exc_info:
            api_client._handle_response_errors(response)

        assert "Request validation failed" in str(exc_info.value)
        assert exc_info.value.status_code == 400

    def test_handle_500_error(self, api_client, mock_response):
        """Test handling of 500 server errors."""
        response = mock_response(status_code=500)

        with pytest.raises(ServerError) as exc_info:
            api_client._handle_response_errors(response)

        assert "Server error: 500" in str(exc_info.value)
        assert exc_info.value.status_code == 500

    def test_handle_success_response(self, api_client, mock_response):
        """Test that successful responses don't raise errors."""
        response = mock_response(status_code=200)
        # Should not raise any exception
        api_client._handle_response_errors(response)


class TestUserMethods:
    """Test user-related API methods."""

    @pytest.mark.asyncio
    async def test_get_user_profile(self, api_client, mock_response, sample_profile):
        """Test getting user profile."""
        profile_data = {
            "username": "testuser",
            "friendly_name": "Test User",
            "student_id": "123456789",
        }

        api_client.client.get.return_value = mock_response(200, profile_data)

        profile = await api_client.get_user_profile()

        assert isinstance(profile, Profile)
        assert profile.username == "testuser"
        assert profile.friendly_name == "Test User"
        assert profile.student_id == "123456789"

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/user/profile"
        )

    @pytest.mark.asyncio
    async def test_get_user_courses(self, api_client, mock_response):
        """Test getting user's courses."""
        courses_data = {
            "courses": [
                {
                    "id": 1,
                    "name": "Test Course",
                    "description": "A test course",
                    "tag": None,
                    "term": None,
                    "url": "/api/v1/course/1",
                    "join_url": None,
                    "quit_url": None,
                    "html_url": "/course/1",
                }
            ]
        }

        api_client.client.get.return_value = mock_response(200, courses_data)

        courses = await api_client.get_user_courses()

        assert len(courses) == 1
        assert courses[0].id == 1
        assert courses[0].name == "Test Course"

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/user/courses"
        )


class TestProblemMethods:
    """Test problem-related API methods."""

    @pytest.mark.asyncio
    async def test_list_problems(self, api_client, mock_response):
        """Test listing problems."""
        problems_data = {
            "problems": [
                {
                    "id": 1000,
                    "title": "A+B Problem",
                    "url": "/api/v1/problem/1000",
                    "submit_url": "/api/v1/problem/1000/submit",
                    "html_url": "/problem/1000",
                }
            ],
            "next": None,
        }

        api_client.client.get.return_value = mock_response(200, problems_data)

        result = await api_client.list_problems()

        assert len(result.problems) == 1
        assert result.problems[0].id == 1000
        assert result.problems[0].title == "A+B Problem"
        assert result.next is None

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/problem/", params={}
        )

    @pytest.mark.asyncio
    async def test_list_problems_with_filters(self, api_client, mock_response):
        """Test listing problems with filters."""
        api_client.client.get.return_value = mock_response(
            200, {"problems": [], "next": None}
        )

        await api_client.list_problems(keyword="test", problemset_id=1, cursor=10)

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/problem/",
            params={"keyword": "test", "problemset_id": 1, "cursor": 10},
        )

    @pytest.mark.asyncio
    async def test_get_problem(self, api_client, mock_response):
        """Test getting a specific problem."""
        problem_data = {
            "id": 1000,
            "title": "A+B Problem",
            "description": "Calculate A+B",
            "input": "Two integers A and B",
            "output": "A+B",
            "examples": [],
            "example_input": "1 2",
            "example_output": "3",
            "data_range": "1 <= A, B <= 100",
            "languages_accepted": ["cpp", "python"],
            "plan_summary": None,
            "attachments": [],
            "allow_public_submissions": True,
        }

        api_client.client.get.return_value = mock_response(200, problem_data)

        problem = await api_client.get_problem(1000)

        assert problem.id == 1000
        assert problem.title == "A+B Problem"
        assert problem.allow_public_submissions is True

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/problem/1000"
        )


class TestSubmissionMethods:
    """Test submission-related API methods."""

    @pytest.mark.asyncio
    async def test_submit_solution(self, api_client, mock_response):
        """Test submitting a solution."""
        submit_data = {"id": 42}

        api_client.client.post.return_value = mock_response(201, submit_data)

        submission_id = await api_client.submit_solution(
            problem_id=1000,
            language="cpp",
            code="#include <iostream>\nint main() { return 0; }",
            public=False,
        )

        assert submission_id == 42

        api_client.client.post.assert_called_once_with(
            "https://test.example.com/api/v1/problem/1000/submit",
            data={
                "language": "cpp",
                "code": "#include <iostream>\nint main() { return 0; }",
                "public": False,
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

    @pytest.mark.asyncio
    async def test_list_submissions(self, api_client, mock_response):
        """Test listing submissions."""
        submissions_data = {
            "submissions": [
                {
                    "id": 42,
                    "friendly_name": "Test User",
                    "problem": {
                        "id": 1000,
                        "title": "A+B Problem",
                        "url": "/api/v1/problem/1000",
                        "submit_url": "/api/v1/problem/1000/submit",
                        "html_url": "/problem/1000",
                    },
                    "status": "accepted",
                    "language": "cpp",
                    "created_at": "2023-01-01T00:00:00Z",
                    "url": "/api/v1/submission/42",
                    "html_url": "/code/42",
                }
            ],
            "next": None,
        }

        api_client.client.get.return_value = mock_response(200, submissions_data)

        result = await api_client.list_submissions()

        assert len(result.submissions) == 1
        assert result.submissions[0].id == 42
        assert result.submissions[0].status == SubmissionStatus.ACCEPTED
        assert result.submissions[0].language == SubmissionLanguage.CPP

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/submission/", params={}
        )

    @pytest.mark.asyncio
    async def test_list_submissions_with_filters(self, api_client, mock_response):
        """Test listing submissions with filters."""
        api_client.client.get.return_value = mock_response(
            200, {"submissions": [], "next": None}
        )

        await api_client.list_submissions(
            username="testuser",
            problem_id=1000,
            status=SubmissionStatus.ACCEPTED,
            language=SubmissionLanguage.CPP,
            cursor=10,
        )

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/submission/",
            params={
                "username": "testuser",
                "problem_id": 1000,
                "status": "accepted",
                "lang": "cpp",
                "cursor": 10,
            },
        )

    @pytest.mark.asyncio
    async def test_get_submission(self, api_client, mock_response):
        """Test getting a specific submission."""
        submission_data = {
            "id": 42,
            "friendly_name": "Test User",
            "problem": {
                "id": 1000,
                "title": "A+B Problem",
                "url": "/api/v1/problem/1000",
                "submit_url": "/api/v1/problem/1000/submit",
                "html_url": "/problem/1000",
            },
            "public": False,
            "language": "cpp",
            "score": 100,
            "message": None,
            "details": None,
            "time_msecs": 100,
            "memory_bytes": 1024,
            "status": "accepted",
            "should_auto_reload": False,
            "should_show_score": True,
            "created_at": "2023-01-01T00:00:00Z",
            "code_url": "/api/v1/submission/42/code",
            "abort_url": None,
            "html_url": "/code/42",
        }

        api_client.client.get.return_value = mock_response(200, submission_data)

        submission = await api_client.get_submission(42)

        assert submission.id == 42
        assert submission.status == SubmissionStatus.ACCEPTED
        assert submission.score == 100
        assert submission.should_auto_reload is False

        api_client.client.get.assert_called_once_with(
            "https://test.example.com/api/v1/submission/42"
        )

    @pytest.mark.asyncio
    async def test_abort_submission(self, api_client, mock_response):
        """Test aborting a submission."""
        api_client.client.post.return_value = mock_response(204)

        await api_client.abort_submission(42)

        api_client.client.post.assert_called_once_with(
            "https://test.example.com/api/v1/submission/42/abort"
        )
