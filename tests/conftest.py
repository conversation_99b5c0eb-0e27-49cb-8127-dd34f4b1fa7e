"""
Pytest configuration and fixtures for termoj tests.
"""

import pytest
import httpx
from unittest.mock import AsyncMock, Mock

from termoj.api_client import ACMOJ<PERSON>lient
from termoj.models import Profile, Course, Problem, Submission


@pytest.fixture
def mock_httpx_client():
    """Mock httpx.AsyncClient for testing."""
    mock_client = AsyncMock(spec=httpx.AsyncClient)
    return mock_client


@pytest.fixture
def api_client(mock_httpx_client):
    """Create an API client with mocked httpx client."""
    client = ACMOJClient("https://test.example.com/api/v1", "test-token")
    client.client = mock_httpx_client
    return client


@pytest.fixture
def sample_profile():
    """Sample user profile for testing."""
    return Profile(
        username="testuser",
        friendly_name="Test User",
        student_id="123456789"
    )


@pytest.fixture
def sample_course():
    """Sample course for testing."""
    return Course(
        id=1,
        name="Test Course",
        description="A test course",
        tag=None,
        term=None,
        url="/api/v1/course/1",
        join_url="/api/v1/course/1/join",
        quit_url="/api/v1/course/1/quit",
        html_url="/course/1"
    )


@pytest.fixture
def sample_problem():
    """Sample problem for testing."""
    return Problem(
        id=1000,
        title="A+B Problem",
        description="Calculate A+B",
        input="Two integers A and B",
        output="A+B",
        examples=[],
        example_input="1 2",
        example_output="3",
        data_range="1 <= A, B <= 100",
        languages_accepted=["cpp", "python"],
        plan_summary=None,
        attachments=[],
        allow_public_submissions=True
    )


@pytest.fixture
def mock_response():
    """Create a mock HTTP response."""
    def _mock_response(status_code=200, json_data=None):
        response = Mock(spec=httpx.Response)
        response.status_code = status_code
        response.is_success = 200 <= status_code < 300
        response.json.return_value = json_data or {}
        response.text = str(json_data) if json_data else ""
        return response
    return _mock_response
