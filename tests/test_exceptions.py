"""
Tests for the exception classes.
"""

import pytest
import httpx
from unittest.mock import Mock

from termoj.exceptions import (
    ACMOJAPIError, AuthenticationError, NotFoundError, 
    ValidationError, ServerError, ConfigurationError
)


class TestACMOJAPIError:
    """Test the base ACMOJAPIError class."""
    
    def test_basic_error(self):
        """Test creating a basic API error."""
        error = ACMOJAPIError("Something went wrong")
        assert str(error) == "Something went wrong"
        assert error.message == "Something went wrong"
        assert error.status_code is None
        assert error.response is None
    
    def test_error_with_status_code(self):
        """Test creating an API error with status code."""
        error = ACMOJAPIError("Bad request", status_code=400)
        assert str(error) == "HTTP 400: Bad request"
        assert error.message == "Bad request"
        assert error.status_code == 400
    
    def test_error_with_response(self):
        """Test creating an API error with response object."""
        mock_response = Mock(spec=httpx.Response)
        mock_response.status_code = 404
        
        error = ACMOJAPIError("Not found", status_code=404, response=mock_response)
        assert error.response == mock_response
        assert error.status_code == 404


class TestSpecificErrors:
    """Test specific error subclasses."""
    
    def test_authentication_error(self):
        """Test AuthenticationError."""
        error = AuthenticationError("Invalid token", status_code=401)
        assert isinstance(error, ACMOJAPIError)
        assert str(error) == "HTTP 401: Invalid token"
    
    def test_not_found_error(self):
        """Test NotFoundError."""
        error = NotFoundError("Resource not found", status_code=404)
        assert isinstance(error, ACMOJAPIError)
        assert str(error) == "HTTP 404: Resource not found"
    
    def test_validation_error(self):
        """Test ValidationError."""
        error = ValidationError("Invalid data", status_code=422)
        assert isinstance(error, ACMOJAPIError)
        assert str(error) == "HTTP 422: Invalid data"
    
    def test_server_error(self):
        """Test ServerError."""
        error = ServerError("Internal server error", status_code=500)
        assert isinstance(error, ACMOJAPIError)
        assert str(error) == "HTTP 500: Internal server error"
    
    def test_configuration_error(self):
        """Test ConfigurationError."""
        error = ConfigurationError("Missing config file")
        assert isinstance(error, Exception)
        assert str(error) == "Missing config file"
        # ConfigurationError is not a subclass of ACMOJAPIError
        assert not isinstance(error, ACMOJAPIError)
