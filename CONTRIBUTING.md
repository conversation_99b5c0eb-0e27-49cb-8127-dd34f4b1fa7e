## **Termoj 项目详细设计文档 (v1.0)**

### **1.0 项目概述**

-   **1.1 项目名称**: `termoj`
-   **1.2 项目简介**: 一个为 ACM/ICPC 风格的在线评测平台（Online Judge, OJ）设计的终端用户界面（TUI）。其设计灵感源于 `lazygit`，旨在为熟悉命令行的用户提供一个高效、信息密度高、键盘驱动的 OJ 交互体验。
-   **1.3 目标用户**: 经常使用特定 OJ 平台进行学习、练习或比赛，并且偏好在终端环境中完成任务的学生与开发者。

### **2.0 核心原则与技术栈**

-   **2.1 设计原则**:
    -   **键盘优先**: 所有核心操作都应通过快捷键完成，鼠标作为辅助。
    -   **上下文感知**: UI 和可用操作应根据用户当前所处的上下文动态变化。
    -   **高信息密度**: 在一屏内尽可能多地展示相关信息，减少不必要的界面跳转。
    -   **职责分离**: `termoj` 是一个交互工具，而非代码编辑器。代码的编写和修改工作应交由用户首选的外部编辑器完成。
-   **2.2 技术栈**:
    -   **语言**: Python (>=3.8)
    -   **TUI 框架**: `textual`
    -   **数据验证**: `pydantic`
    -   **HTTP 客户端**: `httpx`
    -   **平台目录**: `platformdirs`
    -   **开发环境管理**: `uv`

### **3.0 数据模型 (基于 API)**

-   **3.1 数据源**: 项目所有数据均来源于已提供的 OpenAPI v3 规范 (`api.yml`)。
-   **3.2 实现方式**: 使用 `pydantic` 创建与 API `schemas` 中定义的对象一一对应的 Python 类。这将确保数据在应用内部的类型安全和结构一致性。
-   **3.3 核心数据模型**:
    -   `User`: 代表当前用户。
    -   `Course`: 代表班级。
    -   `Problemset`: 代表隶属于班级的作业或比赛。
    -   `Problem` / `ProblemBrief`: 代表题目及其简略信息。
    -   `Submission` / `SubmissionBrief`: 代表提交记录及其简略信息。

### **4.0 用户界面 (UI) 布局与组件**

-   **4.1 总体布局**:
    采用五区域布局，基于 `textual.grid.Grid` 实现。
    -   **网格定义**: 一个两行两列的网格，其中底部区域跨越两列。
        -   列定义：左列占 1/3 宽度，右列占 2/3 宽度。
        -   行定义：主区域占满可用高度，底部区域固定高度（例如 5-7 行）。
    -   **视觉结构**:
        ```
        +------------------+------------------------------+
        | Pane 1: Courses  |                              |
        +------------------+                              |
        | Pane 2: P-sets   | Pane 4: Detail View          |
        +------------------+                              |
        | Pane 3: Problems |                              |
        +------------------+------------------------------+
        | Pane 5: Latest Submission Status              |
        +-----------------------------------------------+
        | Footer: Keybindings                           |
        +-----------------------------------------------+
        ```
-   **4.2 窗格 (Pane) 详细规格**:
    -   **4.2.1 左侧三窗格 (Pane 1, 2, 3)**
        -   **组件**: 每个窗格均由一个 `textual.widgets.DataTable` 实现，用于显示列表数据。
        -   **Pane 1 (Courses)**: 显示 `Course` 列表。标题栏应标示当前模式。
            -   **默认视图**: “我的班级” (`GET /user/courses`)。
            -   **切换视图 (`T` 键)**: “发现班级” (`GET /course/`)。
        -   **Pane 2 (Problemsets)**: 显示 `Problemset` 列表。数据根据 Pane 1 的选择动态加载。
            -   **默认视图**: 所选班级的作业/比赛 (`GET /course/{id}/problemsets`)。
            -   **切换视图 (`T` 键)**: “我的所有作业/比赛” (`GET /user/problemsets`)。
        -   **Pane 3 (Problems)**: 显示 `ProblemBrief` 列表。数据根据 Pane 2 的选择动态加载。
            -   **默认视图**: 所选作业/比赛的题目 (`Problemset` 对象内嵌数据)。
            -   **切换视图 (`T` 键)**: “平台题库” (`GET /problem/`)，需实现滚动到底部自动加载下一页的功能（分页）。
    -   **4.2.2 右侧详情区 (Pane 4)**
        -   **组件**: 使用 `textual_markdown.widgets.Markdown`。
        -   **功能**: 显示左侧任意窗格中当前高亮项目的详细信息。监听 Pane 1, 2, 3 的 `DataTable.RowSelected` 事件，根据选中项的类型和 ID，调用相应的详情接口 (`GET /course/{id}`, `GET /problemset/{id}`, `GET /problem/{id}`) 并渲染返回内容。
    -   **4.2.3 底部提交状态区 (Pane 5)**
        -   **组件**: 使用 `textual.widgets.RichLog`。
        -   **功能**: 显示用户最近一次提交的实时评测状态。该区域在一次成功提交后被激活，并通过轮询 `GET /submission/{id}` 接口进行动态更新。
    -   **4.2.4 页脚 (Footer)**
        -   **组件**: 使用 `textual.widgets.Footer`。
        -   **功能**: 根据当前拥有焦点的窗格，动态显示可用的快捷键提示。

### **5.0 核心工作流与交互逻辑**

-   **5.1 导航与数据加载**:
    -   应用启动时，加载用户配置，并获取“我的班级”列表填充 Pane 1。
    -   用户的选择操作在左侧窗格之间形成一个级联反应：在 Pane N 中选择一项，会触发 API 请求，其结果将用于填充 Pane N+1。
-   **5.2 代码提交工作流**:
    1.  **触发**: 用户在 Pane 3 (Problems) 中聚焦于一个题目，并按下 `S` 键。
    2.  **模态弹窗**: 应用推送一个模态 `Screen` (`SubmissionDialog`) 到视图栈顶。
    3.  **代码自动加载**: `SubmissionDialog` 启动时，按以下顺序自动寻找并加载代码到其只读的 `TextArea` 中：
        1.  **显式关联**: 检查 `config.toml` 中 `[file_associations]` 是否有该题目的记录。若有，加载对应路径的文件。
        2.  **约定路径**: 若无显式关联，则在 `[workspace].base_path` 目录下，根据题目 ID 和 `[language_map]` 中的后缀名，探测约定路径（如 `./{id}.cpp`, `./{id}/main.cpp` 等）。
        3.  **未找到**: 若上述步骤均失败，则在 `TextArea` 中显示提示信息。
    4.  **手动指定 (`O` 键)**: 在 `SubmissionDialog` 中，用户可按 `O` 键，触发另一个更简单的 `PathInputDialog` 弹窗，用于手动输入文件路径。
        -   确认路径后，`SubmissionDialog` 会加载新文件内容，并**立即将此题目 ID 与新路径的关联写入 `config.toml`**。
    5.  **确认提交**: 用户点击“确认”按钮，应用将 `TextArea` 中的代码和所选语言提交到 `POST /problem/{id}/submit`。
    6.  **激活状态轮询**: 提交成功后，`SubmissionDialog` 关闭，主界面下方的 Pane 5 被激活，开始轮询该次提交的状态。
-   **5.3 状态轮询逻辑**:
    -   使用 `set_interval` 创建一个定时器（如每 2 秒一次）。
    -   定时器回调函数请求 `GET /submission/{id}` 接口。
    -   根据返回的 `status` 更新 Pane 5 的 `RichLog` 内容。
    -   当 API 返回的 `should_auto_reload` 字段为 `false` 时，停止该定时器。

### **6.0 配置与数据持久化**

-   **6.1 配置文件**:
    -   **位置**: 由 `platformdirs.user_config_dir('termoj')` 决定，通常为 `~/.config/termoj/config.toml`。
    -   **初始化**: 首次运行时，若配置文件不存在，则自动创建并写入带详细注释的模板。
    -   **结构**: 包含 `[auth]`, `[workspace]`, `[language_map]`, `[file_associations]` 四个主要部分。`[file_associations]` 由程序动态读写。

### **7.0 打包与分发**

-   **7.1 工具**: 使用 `uv` 进行环境和依赖管理。
-   **7.2 配置文件**: 项目打包信息完全由 `pyproject.toml` 定义。
-   **7.3 可执行命令**: 通过在 `pyproject.toml` 中配置 `[project.scripts]`（例如 `termoj = "termoj.main:run_app"`），使得 `pip install` 后，`termoj` 成为一个可在系统任何路径下直接调用的命令。

---

### **待决策的设计问题 (Questions for Project Owner)**

尊敬的项目发起人，以上设计文档已根据我们此前的讨论整理完毕。但在开发前，仍有以下几个细节需要你的最终决策，以确保产品符合你的预期：

-   **问题 1 (UI/UX - 初始焦点)**:
    当 `termoj` 启动并加载完数据后，哪个窗格应该默认获得焦点？

    -   **A)** Pane 1 (Courses) - 符合从上到下的自然流程。
    -   **B)** Pane 3 (Problems) - 如果用户上次操作的是某个作业，直接聚焦到题目列表可能更便捷。
    -   **C)** 其他？

-   **问题 2 (UI/UX - “孤儿”窗格处理)**:
    当用户在 Pane 3 (Problems) 切换到“平台题库”模式时，Pane 1 (Courses) 和 Pane 2 (Problemsets) 的选择就与其无关了。此时这两个“孤儿”窗格应该如何表现？

    -   **A) 变灰/禁用**: 视觉上表示它们当前不活跃。
    -   **B) 显示提示信息**: 例如，显示 "[不适用于题库模式]"。
    -   **C) 自动折叠/隐藏**: 将空间暂时让给 Pane 3，使其可以显示更多题目。（此方案实现较复杂）。

-   **问题 3 (UI/UX - 模糊文件处理)**:
    当代码自动检测发现多个可能的文件时（例如，`1001.cpp` 和 `1001.py` 同时存在），应如何处理？

    -   **A) 显示错误**: 如当前设计，仅提示“发现多个文件，请手动指定”。
    -   **B) 弹出选择列表**: 自动弹出一个简易列表，让用户直接在检测到的几个文件之间选择。

-   **问题 4 (工作流 - 提交语言确定)**:
    在 `SubmissionDialog` 中，提交代码所用的语言是如何确定的？

    -   **A) 根据文件后缀推断**: 使用 `[language_map]` 从文件后缀名（如 `.cpp`）自动推断出语言（`cpp`），用户不能更改。
    -   **B) 用户手动选择**: 弹窗内始终提供一个语言下拉菜单，让用户手动选择，忽略文件名。
    -   **C) 推断并允许覆盖**: 默认根据文件后缀推断，但同时提供一个可交互的下拉菜单，允许用户在提交前更改语言。

-   **问题 5 (外观 - 主题与颜色)**:
    关于 `termoj` 的颜色主题，你的初步想法是？
    -   **A) 使用 Textual 默认主题**: 简洁高效，无需额外工作。
    -   **B) 创建一个自定义的 `termoj` 主题**: 例如，模仿 `lazygit` 的配色。
    -   **C) 尝试适配终端本身的主题色**: 提供更好的原生感。
