"""
ACMOJ API Client

A robust, modern, and asynchronous Python client for the ACM Class OnlineJudge API.
Uses pydantic for data modeling and httpx for HTTP requests.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

import httpx
from pydantic import BaseModel


# Enums
class SubmissionStatus(str, Enum):
    ACCEPTED = "accepted"
    WRONG_ANSWER = "wrong_answer"
    COMPILE_ERROR = "compile_error"
    RUNTIME_ERROR = "runtime_error"
    TIME_LIMIT_EXCEEDED = "time_limit_exceeded"
    MEMORY_LIMIT_EXCEEDED = "memory_limit_exceeded"
    DISK_LIMIT_EXCEEDED = "disk_limit_exceeded"
    MEMORY_LEAK = "memory_leak"
    PENDING = "pending"
    COMPILING = "compiling"
    JUDGING = "judging"
    VOID = "void"
    ABORTED = "aborted"
    SKIPPED = "skipped"
    SYSTEM_ERROR = "system_error"
    BAD_PROBLEM = "bad_problem"
    UNKNOWN_ERROR = "unknown_error"


class SubmissionLanguage(str, Enum):
    CPP = "cpp"
    PYTHON = "python"
    GIT = "git"
    VERILOG = "verilog"
    QUIZ = "quiz"


class ProblemsetType(str, Enum):
    CONTEST = "contest"
    HOMEWORK = "homework"
    EXAM = "exam"


# Pydantic Models
class Profile(BaseModel):
    username: str
    friendly_name: str
    student_id: Optional[str] = None


class CourseTag(BaseModel):
    id: int
    name: str


class Term(BaseModel):
    id: int
    name: str
    start_time: datetime
    end_time: datetime


class Course(BaseModel):
    id: int
    name: str
    description: str
    tag: Optional[CourseTag] = None
    term: Optional[Term] = None
    url: str
    join_url: Optional[str] = None
    quit_url: Optional[str] = None
    html_url: str


class ProblemBrief(BaseModel):
    id: int
    title: Optional[str] = None
    url: Optional[str] = None
    submit_url: Optional[str] = None
    html_url: Optional[str] = None


class ResourceUsage(BaseModel):
    time_msecs: int
    memory_bytes: int
    file_count: int
    file_size_bytes: int


class TestpointSummary(BaseModel):
    id: str
    limits: Optional[ResourceUsage] = None


class SubtaskSummary(BaseModel):
    id: str
    name: str
    testpoints: List[TestpointSummary]
    score: float


class JudgePlanSummary(BaseModel):
    subtasks: List[SubtaskSummary]


class ProblemExample(BaseModel):
    name: Optional[str] = None
    input: Optional[str] = None
    output: Optional[str] = None
    description: Optional[str] = None


class ProblemAttachment(BaseModel):
    name: str
    size_bytes: int
    url: str


class Problem(BaseModel):
    id: int
    title: str
    description: Optional[str] = None
    input: Optional[str] = None
    output: Optional[str] = None
    examples: List[ProblemExample] = []
    example_input: Optional[str] = None
    example_output: Optional[str] = None
    data_range: Optional[str] = None
    languages_accepted: List[SubmissionLanguage] = []
    plan_summary: Optional[JudgePlanSummary] = None
    attachments: List[ProblemAttachment] = []
    allow_public_submissions: bool


class SubmissionBrief(BaseModel):
    id: int
    friendly_name: str
    problem: ProblemBrief
    status: SubmissionStatus
    language: SubmissionLanguage
    created_at: datetime
    url: Optional[str] = None
    html_url: Optional[str] = None


class Submission(BaseModel):
    id: int
    friendly_name: str
    problem: ProblemBrief
    public: bool
    language: SubmissionLanguage
    score: int
    message: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    time_msecs: Optional[int] = None
    memory_bytes: Optional[int] = None
    status: SubmissionStatus
    should_auto_reload: bool
    should_show_score: bool
    created_at: datetime
    code_url: str
    abort_url: Optional[str] = None
    html_url: str


class Problemset(BaseModel):
    id: int
    course: Course
    name: str
    description: str
    allowed_languages: List[SubmissionLanguage] = []
    start_time: datetime
    end_time: datetime
    late_submission_deadline: Optional[datetime] = None
    type: ProblemsetType
    problems: List[ProblemBrief] = []
    url: str
    join_url: Optional[str] = None
    quit_url: Optional[str] = None
    html_url: str


# Token Response Models
class TokenResponse(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    scope: str


# List Response Models
class ProblemsResponse(BaseModel):
    problems: List[ProblemBrief]
    next: Optional[str] = None


class SubmissionsResponse(BaseModel):
    submissions: List[SubmissionBrief]
    next: Optional[str] = None


class CoursesResponse(BaseModel):
    courses: List[Course]
    next: Optional[str] = None


class ProblemsetsResponse(BaseModel):
    problemsets: List[Problemset]


class UserCoursesResponse(BaseModel):
    courses: List[Course]


class UserProblemsetsResponse(BaseModel):
    problemsets: List[Problemset]


class SubmitResponse(BaseModel):
    id: int


# Exception Classes
class ACMOJAPIError(Exception):
    """Base exception for ACMOJ API errors."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response: Optional[httpx.Response] = None,
    ):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.response = response


class AuthenticationError(ACMOJAPIError):
    """Raised when authentication fails (401/403)."""

    pass


class NotFoundError(ACMOJAPIError):
    """Raised when a resource is not found (404)."""

    pass


class ValidationError(ACMOJAPIError):
    """Raised when request validation fails (400/422)."""

    pass


class ServerError(ACMOJAPIError):
    """Raised when server encounters an error (5xx)."""

    pass


# Main API Client
class ACMOJClient:
    """
    Asynchronous client for the ACMOJ API.

    This client provides methods to interact with all endpoints of the ACMOJ API,
    including user management, problem access, submission handling, and course operations.
    """

    def __init__(self, base_url: str, token: str):
        """
        Initialize the ACMOJ API client.

        Args:
            base_url: The base URL of the API (e.g., "https://acm.sjtu.edu.cn/OnlineJudge/api/v1")
            token: The bearer token for authentication
        """
        self.base_url = base_url.rstrip("/")
        self.token = token
        self.client = httpx.AsyncClient(
            headers={"Authorization": f"Bearer {token}"}, timeout=30.0
        )

    async def close(self):
        """Close the HTTP client session."""
        await self.client.aclose()

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
        return None

    def _handle_response_errors(self, response: httpx.Response):
        """Handle HTTP response errors and raise appropriate exceptions."""
        if response.status_code == 401:
            raise AuthenticationError(
                "Authentication failed - invalid or expired token",
                response.status_code,
                response,
            )
        elif response.status_code == 403:
            raise AuthenticationError(
                "Access forbidden - insufficient permissions",
                response.status_code,
                response,
            )
        elif response.status_code == 404:
            raise NotFoundError("Resource not found", response.status_code, response)
        elif response.status_code == 400 or response.status_code == 422:
            raise ValidationError(
                "Request validation failed", response.status_code, response
            )
        elif 500 <= response.status_code < 600:
            raise ServerError(
                f"Server error: {response.status_code}", response.status_code, response
            )
        elif not response.is_success:
            raise ACMOJAPIError(
                f"HTTP {response.status_code}: {response.text}",
                response.status_code,
                response,
            )

    # OAuth Methods
    async def get_access_token(
        self,
        client_id: str,
        client_secret: str,
        code: str,
        redirect_uri: Optional[str] = None,
    ) -> TokenResponse:
        """
        Get access token using authorization code.

        Args:
            client_id: OAuth client ID
            client_secret: OAuth client secret
            code: Authorization code from frontend
            redirect_uri: Optional redirect URI for validation

        Returns:
            TokenResponse containing access token and metadata

        Raises:
            ACMOJAPIError: If token request fails
        """
        data = {
            "grant_type": "authorization_code",
            "client_id": client_id,
            "client_secret": client_secret,
            "code": code,
        }
        if redirect_uri:
            data["redirect_uri"] = redirect_uri

        response = await self.client.post(
            f"{self.base_url}/oauth/token",
            data=data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )
        self._handle_response_errors(response)
        return TokenResponse(**response.json())

    # User Methods
    async def get_user_profile(self) -> Profile:
        """
        Get current user profile information.

        Returns:
            Profile: User profile data

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/user/profile")
        self._handle_response_errors(response)
        return Profile(**response.json())

    async def get_user_courses(self) -> List[Course]:
        """
        Get courses that the current user has joined.

        Returns:
            List[Course]: List of courses the user is enrolled in

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/user/courses")
        self._handle_response_errors(response)
        data = UserCoursesResponse(**response.json())
        return data.courses

    async def get_user_problemsets(self) -> List[Problemset]:
        """
        Get problemsets (contests/homework) that the current user has joined.

        Returns:
            List[Problemset]: List of problemsets the user is enrolled in

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/user/problemsets")
        self._handle_response_errors(response)
        data = UserProblemsetsResponse(**response.json())
        return data.problemsets

    # Problem Methods
    async def list_problems(
        self,
        keyword: Optional[str] = None,
        problemset_id: Optional[int] = None,
        cursor: Optional[int] = None,
    ) -> ProblemsResponse:
        """
        List problems with optional filtering.

        Args:
            keyword: Search keyword for problem titles
            problemset_id: Filter by specific problemset ID
            cursor: Pagination cursor

        Returns:
            ProblemsResponse: List of problems and pagination info

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        params = {}
        if keyword:
            params["keyword"] = keyword
        if problemset_id:
            params["problemset_id"] = problemset_id
        if cursor:
            params["cursor"] = cursor

        response = await self.client.get(f"{self.base_url}/problem/", params=params)
        self._handle_response_errors(response)
        return ProblemsResponse(**response.json())

    async def get_problem(self, problem_id: int) -> Problem:
        """
        Get detailed information about a specific problem.

        Args:
            problem_id: The ID of the problem to retrieve

        Returns:
            Problem: Detailed problem information

        Raises:
            NotFoundError: If problem doesn't exist
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/problem/{problem_id}")
        self._handle_response_errors(response)
        return Problem(**response.json())

    # Submission Methods
    async def submit_solution(
        self, problem_id: int, language: str, code: str, public: bool = False
    ) -> int:
        """
        Submit a solution to a problem.

        Args:
            problem_id: The ID of the problem to submit to
            language: Programming language (e.g., 'cpp', 'python')
            code: Source code to submit
            public: Whether to make the submission public

        Returns:
            int: The submission ID

        Raises:
            NotFoundError: If problem doesn't exist
            AuthenticationError: If authentication fails
            ValidationError: If submission data is invalid
            ACMOJAPIError: If request fails
        """
        data = {"language": language, "code": code, "public": public}

        response = await self.client.post(
            f"{self.base_url}/problem/{problem_id}/submit",
            data=data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )
        self._handle_response_errors(response)
        result = SubmitResponse(**response.json())
        return result.id

    async def list_submissions(
        self,
        username: Optional[str] = None,
        problem_id: Optional[int] = None,
        status: Optional[SubmissionStatus] = None,
        language: Optional[SubmissionLanguage] = None,
        cursor: Optional[int] = None,
    ) -> SubmissionsResponse:
        """
        List submissions with optional filtering.

        Args:
            username: Filter by specific username
            problem_id: Filter by specific problem ID
            status: Filter by submission status
            language: Filter by programming language
            cursor: Pagination cursor

        Returns:
            SubmissionsResponse: List of submissions and pagination info

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        params = {}
        if username:
            params["username"] = username
        if problem_id:
            params["problem_id"] = problem_id
        if status:
            params["status"] = status.value
        if language:
            params["lang"] = language.value
        if cursor:
            params["cursor"] = cursor

        response = await self.client.get(f"{self.base_url}/submission/", params=params)
        self._handle_response_errors(response)
        return SubmissionsResponse(**response.json())

    async def get_submission(self, submission_id: int) -> Submission:
        """
        Get detailed information about a specific submission.

        Args:
            submission_id: The ID of the submission to retrieve

        Returns:
            Submission: Detailed submission information

        Raises:
            NotFoundError: If submission doesn't exist
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/submission/{submission_id}")
        self._handle_response_errors(response)
        return Submission(**response.json())

    async def abort_submission(self, submission_id: int) -> None:
        """
        Abort a running submission.

        Args:
            submission_id: The ID of the submission to abort

        Raises:
            NotFoundError: If submission doesn't exist
            AuthenticationError: If authentication fails or insufficient permissions
            ACMOJAPIError: If request fails
        """
        response = await self.client.post(
            f"{self.base_url}/submission/{submission_id}/abort"
        )
        self._handle_response_errors(response)

    # Problemset Methods
    async def get_problemset(self, problemset_id: int) -> Problemset:
        """
        Get detailed information about a specific problemset.

        Args:
            problemset_id: The ID of the problemset to retrieve

        Returns:
            Problemset: Detailed problemset information

        Raises:
            NotFoundError: If problemset doesn't exist
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/problemset/{problemset_id}")
        self._handle_response_errors(response)
        return Problemset(**response.json())

    async def join_problemset(self, problemset_id: int) -> None:
        """
        Join a problemset (contest/homework).

        Args:
            problemset_id: The ID of the problemset to join

        Raises:
            NotFoundError: If problemset doesn't exist
            AuthenticationError: If authentication fails or cannot join
            ACMOJAPIError: If request fails
        """
        response = await self.client.post(
            f"{self.base_url}/problemset/{problemset_id}/join"
        )
        self._handle_response_errors(response)

    async def quit_problemset(self, problemset_id: int) -> None:
        """
        Quit a problemset (contest/homework).

        Args:
            problemset_id: The ID of the problemset to quit

        Raises:
            NotFoundError: If problemset doesn't exist
            AuthenticationError: If authentication fails or cannot quit
            ACMOJAPIError: If request fails
        """
        response = await self.client.post(
            f"{self.base_url}/problemset/{problemset_id}/quit"
        )
        self._handle_response_errors(response)

    # Course Methods
    async def list_courses(
        self,
        keyword: Optional[str] = None,
        term: Optional[int] = None,
        tag: Optional[int] = None,
        cursor: Optional[int] = None,
    ) -> CoursesResponse:
        """
        List courses with optional filtering.

        Args:
            keyword: Search keyword for course names
            term: Filter by specific term ID
            tag: Filter by specific tag ID
            cursor: Pagination cursor

        Returns:
            CoursesResponse: List of courses and pagination info

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        params = {}
        if keyword:
            params["keyword"] = keyword
        if term:
            params["term"] = term
        if tag:
            params["tag"] = tag
        if cursor:
            params["cursor"] = cursor

        response = await self.client.get(f"{self.base_url}/course/", params=params)
        self._handle_response_errors(response)
        return CoursesResponse(**response.json())

    async def get_course(self, course_id: int) -> Course:
        """
        Get detailed information about a specific course.

        Args:
            course_id: The ID of the course to retrieve

        Returns:
            Course: Detailed course information

        Raises:
            NotFoundError: If course doesn't exist
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/course/{course_id}")
        self._handle_response_errors(response)
        return Course(**response.json())

    async def join_course(self, course_id: int) -> None:
        """
        Join a course.

        Args:
            course_id: The ID of the course to join

        Raises:
            NotFoundError: If course doesn't exist
            AuthenticationError: If authentication fails or cannot join
            ACMOJAPIError: If request fails
        """
        response = await self.client.post(f"{self.base_url}/course/{course_id}/join")
        self._handle_response_errors(response)

    async def quit_course(self, course_id: int) -> None:
        """
        Quit a course.

        Args:
            course_id: The ID of the course to quit

        Raises:
            NotFoundError: If course doesn't exist
            AuthenticationError: If authentication fails or cannot quit
            ValidationError: If not enrolled in the course
            ACMOJAPIError: If request fails
        """
        response = await self.client.post(f"{self.base_url}/course/{course_id}/quit")
        self._handle_response_errors(response)

    async def get_course_problemsets(self, course_id: int) -> List[Problemset]:
        """
        Get problemsets (contests/homework) for a specific course.

        Args:
            course_id: The ID of the course

        Returns:
            List[Problemset]: List of problemsets in the course

        Raises:
            NotFoundError: If course doesn't exist
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(
            f"{self.base_url}/course/{course_id}/problemsets"
        )
        self._handle_response_errors(response)
        data = ProblemsetsResponse(**response.json())
        return data.problemsets

    # Meta Methods
    async def get_judge_status_info(self) -> Dict[str, Dict[str, str]]:
        """
        Get information about judge status codes and their display properties.

        Returns:
            Dict mapping status codes to their display information

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/meta/info/judge-status")
        self._handle_response_errors(response)
        return response.json()

    async def get_language_info(self) -> Dict[str, Dict[str, Optional[str]]]:
        """
        Get information about programming languages and their properties.

        Returns:
            Dict mapping language codes to their display information

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/meta/info/language")
        self._handle_response_errors(response)
        return response.json()

    async def get_runner_status(self) -> List[Dict[str, Any]]:
        """
        Get status information about judge runners.

        Returns:
            List of runner status information

        Raises:
            AuthenticationError: If authentication fails
            ACMOJAPIError: If request fails
        """
        response = await self.client.get(f"{self.base_url}/meta/runner-status")
        self._handle_response_errors(response)
        return response.json()
